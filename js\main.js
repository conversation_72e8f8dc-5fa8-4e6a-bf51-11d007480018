// Main entry point for Neon Brick Breaker

class GameManager {
    constructor() {
        this.game = null;
        this.canvas = null;
        this.animationId = null;
        this.initialized = false;
    }
    
    init() {
        // Get canvas element
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            console.error('Canvas element not found!');
            return;
        }
        
        // Initialize systems
        Input.init(this.canvas);
        audioManager.init();
        
        // Create game instance
        this.game = new Game(this.canvas);
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Start game loop
        this.startGameLoop();
        
        this.initialized = true;
        console.log('Neon Brick Breaker initialized successfully!');
    }
    
    setupEventListeners() {
        // Start button
        document.getElementById('startButton').addEventListener('click', () => {
            this.game.startGame();
            audioManager.resume(); // Resume audio context for browsers that require user interaction
        });
        
        // Restart button
        document.getElementById('restartButton').addEventListener('click', () => {
            this.game.resetGame();
        });
        
        // Menu buttons
        document.getElementById('menuButton').addEventListener('click', () => {
            this.game.resetGame();
        });
        
        document.getElementById('menuFromVictoryButton').addEventListener('click', () => {
            this.game.resetGame();
        });
        
        document.getElementById('menuFromPauseButton').addEventListener('click', () => {
            this.game.resetGame();
        });
        
        // Next level button
        document.getElementById('nextLevelButton').addEventListener('click', () => {
            this.nextLevel();
        });
        
        // Resume button
        document.getElementById('resumeButton').addEventListener('click', () => {
            this.game.resumeGame();
        });
        
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    if (this.game.state === 'playing') {
                        this.game.launchBall();
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    if (this.game.state === 'playing') {
                        this.game.pauseGame();
                    } else if (this.game.state === 'paused') {
                        this.game.resumeGame();
                    }
                    break;
                case 'KeyR':
                    e.preventDefault();
                    if (this.game.state === 'gameOver' || this.game.state === 'start') {
                        this.game.resetGame();
                    }
                    break;
            }
        });
        
        // Touch/click to launch ball
        this.canvas.addEventListener('click', () => {
            if (this.game.state === 'playing') {
                this.game.launchBall();
            }
        });
        
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            if (this.game.state === 'playing') {
                this.game.launchBall();
            }
        });
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
        
        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.game.state === 'playing') {
                this.game.pauseGame();
            }
        });
        
        // Handle window focus/blur
        window.addEventListener('blur', () => {
            if (this.game.state === 'playing') {
                this.game.pauseGame();
            }
        });
    }
    
    nextLevel() {
        this.game.level++;
        this.game.state = 'playing';
        this.game.hideAllScreens();
        
        // Create new level with more bricks or different layout
        this.createLevelBricks();
        
        // Reset ball
        this.game.createBall();
        
        // Clear effects
        this.game.particleSystem.clear();
        this.game.trailSystem.clear();
        
        this.game.updateUI();
    }
    
    createLevelBricks() {
        this.game.bricks = [];
        const level = this.game.level;
        const rows = Math.min(6 + Math.floor(level / 3), 10);
        const cols = 10;
        const brickWidth = 70;
        const brickHeight = 25;
        const padding = 5;
        const offsetX = (this.game.width - (cols * (brickWidth + padding) - padding)) / 2;
        const offsetY = 80;
        
        const colors = [Colors.neonPink, Colors.neonBlue, Colors.neonGreen, 
                       Colors.neonPurple, Colors.neonYellow, Colors.neonOrange];
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                // Skip some bricks for higher levels to create patterns
                if (level > 2 && Math.random() < 0.1) continue;
                
                this.game.bricks.push({
                    x: offsetX + col * (brickWidth + padding),
                    y: offsetY + row * (brickHeight + padding),
                    width: brickWidth,
                    height: brickHeight,
                    color: colors[row % colors.length],
                    health: Math.min(Math.floor(level / 4) + 1, 3),
                    points: (rows - row) * 10 * level,
                    destroyed: false
                });
            }
        }
        
        // Increase ball speed slightly each level
        this.game.ball.speed = 300 + (level - 1) * 20;
    }
    
    startGameLoop() {
        const gameLoop = (currentTime) => {
            // Update game
            if (this.game) {
                this.game.update(currentTime);
                this.game.render();
            }
            
            // Continue loop
            this.animationId = requestAnimationFrame(gameLoop);
        };
        
        this.animationId = requestAnimationFrame(gameLoop);
    }
    
    stopGameLoop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
    
    destroy() {
        this.stopGameLoop();
        this.game = null;
        this.initialized = false;
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const gameManager = new GameManager();
    gameManager.init();
    
    // Make gameManager globally accessible for debugging
    window.gameManager = gameManager;
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.gameManager) {
        window.gameManager.destroy();
    }
});
