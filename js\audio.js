// Audio system for the Neon Brick Breaker game

class AudioManager {
    constructor() {
        this.sounds = {};
        this.enabled = true;
        this.volume = 0.7;
        this.initialized = false;
    }
    
    // Initialize audio system
    init() {
        // Get audio elements from DOM
        this.sounds = {
            paddleHit: document.getElementById('paddleHitSound'),
            brickHit: document.getElementById('brickHitSound'),
            wallHit: document.getElementById('wallHitSound'),
            gameOver: document.getElementById('gameOverSound'),
            levelComplete: document.getElementById('levelCompleteSound')
        };
        
        // Set initial volumes
        Object.values(this.sounds).forEach(sound => {
            if (sound) {
                sound.volume = this.volume;
            }
        });
        
        // Create synthetic sounds if audio files are not available
        this.createSyntheticSounds();
        
        this.initialized = true;
    }
    
    // Create synthetic sounds using Web Audio API
    createSyntheticSounds() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.syntheticSounds = {};
            
            // Create synthetic sound generators
            this.syntheticSounds.paddleHit = () => this.createTone(220, 0.1, 'square');
            this.syntheticSounds.brickHit = () => this.createTone(440, 0.15, 'sawtooth');
            this.syntheticSounds.wallHit = () => this.createTone(330, 0.1, 'triangle');
            this.syntheticSounds.gameOver = () => this.createSequence([
                { freq: 220, duration: 0.3, type: 'sine' },
                { freq: 165, duration: 0.3, type: 'sine' },
                { freq: 110, duration: 0.5, type: 'sine' }
            ]);
            this.syntheticSounds.levelComplete = () => this.createSequence([
                { freq: 330, duration: 0.2, type: 'sine' },
                { freq: 440, duration: 0.2, type: 'sine' },
                { freq: 550, duration: 0.3, type: 'sine' }
            ]);
            
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
        }
    }
    
    // Create a tone using Web Audio API
    createTone(frequency, duration, type = 'sine') {
        if (!this.audioContext || !this.enabled) return;
        
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            oscillator.type = type;
            
            // Envelope for smoother sound
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
            
        } catch (error) {
            console.warn('Error creating tone:', error);
        }
    }
    
    // Create a sequence of tones
    createSequence(notes) {
        if (!this.audioContext || !this.enabled) return;
        
        let currentTime = this.audioContext.currentTime;
        
        notes.forEach(note => {
            try {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(this.audioContext.destination);
                
                oscillator.frequency.setValueAtTime(note.freq, currentTime);
                oscillator.type = note.type || 'sine';
                
                gainNode.gain.setValueAtTime(0, currentTime);
                gainNode.gain.linearRampToValueAtTime(this.volume * 0.2, currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.001, currentTime + note.duration);
                
                oscillator.start(currentTime);
                oscillator.stop(currentTime + note.duration);
                
                currentTime += note.duration;
                
            } catch (error) {
                console.warn('Error in sequence:', error);
            }
        });
    }
    
    // Play a sound
    play(soundName) {
        if (!this.enabled || !this.initialized) return;
        
        // Try to play HTML audio first
        const htmlSound = this.sounds[soundName];
        if (htmlSound && htmlSound.readyState >= 2) {
            try {
                htmlSound.currentTime = 0;
                htmlSound.play().catch(error => {
                    console.warn('HTML audio play failed:', error);
                    this.playSynthetic(soundName);
                });
                return;
            } catch (error) {
                console.warn('HTML audio error:', error);
            }
        }
        
        // Fallback to synthetic sound
        this.playSynthetic(soundName);
    }
    
    // Play synthetic sound
    playSynthetic(soundName) {
        if (this.syntheticSounds && this.syntheticSounds[soundName]) {
            this.syntheticSounds[soundName]();
        }
    }
    
    // Set volume for all sounds
    setVolume(volume) {
        this.volume = Utils.clamp(volume, 0, 1);
        
        Object.values(this.sounds).forEach(sound => {
            if (sound) {
                sound.volume = this.volume;
            }
        });
    }
    
    // Toggle audio on/off
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
    
    // Enable audio
    enable() {
        this.enabled = true;
    }
    
    // Disable audio
    disable() {
        this.enabled = false;
    }
    
    // Check if audio is enabled
    isEnabled() {
        return this.enabled;
    }
    
    // Resume audio context (required for some browsers)
    resume() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }
}

// Global audio manager instance
const audioManager = new AudioManager();
