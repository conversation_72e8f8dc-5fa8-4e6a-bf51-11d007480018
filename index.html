<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Brick Breaker</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- Game UI Overlay -->
        <div class="ui-overlay">
            <div class="top-bar">
                <div class="score-display">
                    <span class="label">SCORE</span>
                    <span class="value" id="score">0</span>
                </div>
                <div class="lives-display">
                    <span class="label">LIVES</span>
                    <span class="value" id="lives">3</span>
                </div>
                <div class="level-display">
                    <span class="label">LEVEL</span>
                    <span class="value" id="level">1</span>
                </div>
            </div>
        </div>

        <!-- Main Game Canvas -->
        <canvas id="gameCanvas"></canvas>

        <!-- Game Screens -->
        <div class="screen" id="startScreen">
            <div class="screen-content">
                <h1 class="game-title">NEON<br>BRICK BREAKER</h1>
                <p class="game-subtitle">Break through the neon barriers</p>
                <button type="button" class="neon-button" id="startButton">START GAME</button>
                <div class="controls-info">
                    <p>CONTROLS:</p>
                    <p>Mouse / Arrow Keys / Touch to move paddle</p>
                    <p>SPACE to launch ball</p>
                </div>
            </div>
        </div>

        <div class="screen" id="gameOverScreen">
            <div class="screen-content">
                <h2 class="screen-title">GAME OVER</h2>
                <div class="final-score">
                    <span class="label">FINAL SCORE</span>
                    <span class="value" id="finalScore">0</span>
                </div>
                <button type="button" class="neon-button" id="restartButton">PLAY AGAIN</button>
                <button type="button" class="neon-button secondary" id="menuButton">MAIN MENU</button>
            </div>
        </div>

        <div class="screen" id="victoryScreen">
            <div class="screen-content">
                <h2 class="screen-title">LEVEL COMPLETE!</h2>
                <div class="level-stats">
                    <div class="stat">
                        <span class="label">SCORE</span>
                        <span class="value" id="levelScore">0</span>
                    </div>
                    <div class="stat">
                        <span class="label">BONUS</span>
                        <span class="value" id="levelBonus">0</span>
                    </div>
                </div>
                <button type="button" class="neon-button" id="nextLevelButton">NEXT LEVEL</button>
                <button type="button" class="neon-button secondary" id="menuFromVictoryButton">MAIN MENU</button>
            </div>
        </div>

        <div class="screen" id="pauseScreen">
            <div class="screen-content">
                <h2 class="screen-title">PAUSED</h2>
                <button type="button" class="neon-button" id="resumeButton">RESUME</button>
                <button type="button" class="neon-button secondary" id="menuFromPauseButton">MAIN MENU</button>
            </div>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="paddleHitSound" preload="auto">
        <source src="sounds/paddle-hit.mp3" type="audio/mpeg">
        <source src="sounds/paddle-hit.ogg" type="audio/ogg">
    </audio>
    <audio id="brickHitSound" preload="auto">
        <source src="sounds/brick-hit.mp3" type="audio/mpeg">
        <source src="sounds/brick-hit.ogg" type="audio/ogg">
    </audio>
    <audio id="wallHitSound" preload="auto">
        <source src="sounds/wall-hit.mp3" type="audio/mpeg">
        <source src="sounds/wall-hit.ogg" type="audio/ogg">
    </audio>
    <audio id="gameOverSound" preload="auto">
        <source src="sounds/game-over.mp3" type="audio/mpeg">
        <source src="sounds/game-over.ogg" type="audio/ogg">
    </audio>
    <audio id="levelCompleteSound" preload="auto">
        <source src="sounds/level-complete.mp3" type="audio/mpeg">
        <source src="sounds/level-complete.ogg" type="audio/ogg">
    </audio>

    <!-- Game Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
