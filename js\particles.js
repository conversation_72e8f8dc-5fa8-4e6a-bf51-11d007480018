// Particle system for neon effects and animations

class Particle {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.vx = options.vx || Utils.random(-2, 2);
        this.vy = options.vy || Utils.random(-2, 2);
        this.life = options.life || 1.0;
        this.maxLife = this.life;
        this.size = options.size || Utils.random(2, 6);
        this.color = options.color || Colors.randomNeon();
        this.gravity = options.gravity || 0;
        this.friction = options.friction || 0.98;
        this.glow = options.glow !== false;
        this.fadeOut = options.fadeOut !== false;
        this.type = options.type || 'circle';
    }
    
    update(deltaTime) {
        // Update position
        this.x += this.vx * deltaTime * 60;
        this.y += this.vy * deltaTime * 60;
        
        // Apply gravity
        this.vy += this.gravity * deltaTime * 60;
        
        // Apply friction
        this.vx *= this.friction;
        this.vy *= this.friction;
        
        // Update life
        this.life -= deltaTime;
        
        return this.life > 0;
    }
    
    render(ctx) {
        const alpha = this.fadeOut ? this.life / this.maxLife : 1;
        
        ctx.save();
        ctx.globalAlpha = alpha;
        
        if (this.glow) {
            // Create glow effect
            const gradient = Colors.createGlowGradient(ctx, this.x, this.y, this.size * 2, this.color);
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size * 2, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // Draw main particle
        ctx.fillStyle = this.color;
        ctx.beginPath();
        
        switch (this.type) {
            case 'circle':
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                break;
            case 'square':
                ctx.rect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
                break;
            case 'star':
                this.drawStar(ctx, this.x, this.y, this.size);
                break;
        }
        
        ctx.fill();
        ctx.restore();
    }
    
    drawStar(ctx, x, y, size) {
        const spikes = 5;
        const outerRadius = size;
        const innerRadius = size * 0.4;
        
        ctx.beginPath();
        for (let i = 0; i < spikes * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / spikes;
            const px = x + Math.cos(angle) * radius;
            const py = y + Math.sin(angle) * radius;
            
            if (i === 0) ctx.moveTo(px, py);
            else ctx.lineTo(px, py);
        }
        ctx.closePath();
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
        this.trails = new Map(); // For ball trails
    }
    
    // Add explosion effect
    addExplosion(x, y, color, count = 15) {
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const speed = Utils.random(50, 150);
            const particle = new Particle(x, y, {
                vx: Math.cos(angle) * speed / 60,
                vy: Math.sin(angle) * speed / 60,
                life: Utils.random(0.5, 1.5),
                size: Utils.random(2, 5),
                color: color,
                gravity: 0.1,
                friction: 0.95,
                type: Utils.randomInt(0, 2) === 0 ? 'circle' : 'star'
            });
            this.particles.push(particle);
        }
    }
    
    // Add sparkle effect
    addSparkles(x, y, color, count = 8) {
        for (let i = 0; i < count; i++) {
            const particle = new Particle(x + Utils.random(-10, 10), y + Utils.random(-10, 10), {
                vx: Utils.random(-30, 30) / 60,
                vy: Utils.random(-30, 30) / 60,
                life: Utils.random(0.3, 0.8),
                size: Utils.random(1, 3),
                color: color,
                gravity: 0,
                friction: 0.99,
                type: 'star'
            });
            this.particles.push(particle);
        }
    }
    
    // Add trail particle
    addTrail(x, y, color, size = 3) {
        const particle = new Particle(x, y, {
            vx: 0,
            vy: 0,
            life: 0.3,
            size: size,
            color: color,
            gravity: 0,
            friction: 1,
            glow: true
        });
        this.particles.push(particle);
    }
    
    // Add floating particles for ambient effect
    addFloatingParticles(x, y, width, height, count = 5) {
        for (let i = 0; i < count; i++) {
            const particle = new Particle(
                x + Utils.random(0, width),
                y + Utils.random(0, height),
                {
                    vx: Utils.random(-10, 10) / 60,
                    vy: Utils.random(-20, -5) / 60,
                    life: Utils.random(2, 4),
                    size: Utils.random(1, 2),
                    color: Colors.randomNeon(),
                    gravity: 0,
                    friction: 0.999,
                    glow: true
                }
            );
            this.particles.push(particle);
        }
    }
    
    // Add power-up effect
    addPowerUpEffect(x, y, color) {
        // Ring of particles
        for (let i = 0; i < 12; i++) {
            const angle = (Math.PI * 2 * i) / 12;
            const radius = 20;
            const px = x + Math.cos(angle) * radius;
            const py = y + Math.sin(angle) * radius;
            
            const particle = new Particle(px, py, {
                vx: Math.cos(angle) * 30 / 60,
                vy: Math.sin(angle) * 30 / 60,
                life: 1.0,
                size: 4,
                color: color,
                gravity: 0,
                friction: 0.98,
                type: 'star'
            });
            this.particles.push(particle);
        }
    }
    
    // Update all particles
    update(deltaTime) {
        this.particles = this.particles.filter(particle => particle.update(deltaTime));
    }
    
    // Render all particles
    render(ctx) {
        this.particles.forEach(particle => particle.render(ctx));
    }
    
    // Clear all particles
    clear() {
        this.particles = [];
    }
    
    // Get particle count
    getCount() {
        return this.particles.length;
    }
}

// Trail system for ball movement
class TrailSystem {
    constructor() {
        this.trails = [];
        this.maxTrails = 20;
    }
    
    addTrailPoint(x, y, color) {
        this.trails.push({
            x: x,
            y: y,
            color: color,
            life: 1.0,
            size: 3
        });
        
        // Remove old trails
        if (this.trails.length > this.maxTrails) {
            this.trails.shift();
        }
    }
    
    update(deltaTime) {
        this.trails.forEach(trail => {
            trail.life -= deltaTime * 3; // Fade faster than particles
            trail.size *= 0.98;
        });
        
        // Remove dead trails
        this.trails = this.trails.filter(trail => trail.life > 0);
    }
    
    render(ctx) {
        this.trails.forEach((trail, index) => {
            const alpha = trail.life * (index / this.trails.length);
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            // Glow effect
            const gradient = Colors.createGlowGradient(ctx, trail.x, trail.y, trail.size * 2, trail.color);
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(trail.x, trail.y, trail.size * 2, 0, Math.PI * 2);
            ctx.fill();
            
            // Main trail point
            ctx.fillStyle = trail.color;
            ctx.beginPath();
            ctx.arc(trail.x, trail.y, trail.size, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        });
    }
    
    clear() {
        this.trails = [];
    }
}
