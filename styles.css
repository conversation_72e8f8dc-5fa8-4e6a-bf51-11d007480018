/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: radial-gradient(ellipse at center, #1a0033 0%, #000000 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    user-select: none;
}

/* Game Container */
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Canvas Styles */
#gameCanvas {
    border: 2px solid #00ffff;
    border-radius: 10px;
    box-shadow: 
        0 0 20px #00ffff,
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.8);
    max-width: 90vw;
    max-height: 90vh;
}

/* UI Overlay */
.ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    pointer-events: none;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
}

.score-display, .lives-display, .level-display {
    text-align: center;
}

.top-bar .label {
    display: block;
    font-size: 12px;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff;
    margin-bottom: 5px;
    letter-spacing: 2px;
}

.top-bar .value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 15px #ff00ff;
}

/* Screen Overlays */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.screen.active {
    opacity: 1;
    visibility: visible;
}

.screen-content {
    text-align: center;
    max-width: 500px;
    padding: 40px;
}

/* Typography */
.game-title {
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    margin-bottom: 20px;
    line-height: 1.1;
}

.game-subtitle {
    font-size: 1.2rem;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff;
    margin-bottom: 40px;
    opacity: 0.8;
}

.screen-title {
    font-size: 3rem;
    font-weight: 700;
    color: #ff00ff;
    text-shadow: 0 0 20px #ff00ff;
    margin-bottom: 30px;
}

/* Buttons */
.neon-button {
    background: transparent;
    border: 2px solid #00ffff;
    color: #00ffff;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    padding: 15px 30px;
    margin: 10px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
}

.neon-button:hover {
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 
        0 0 20px #00ffff,
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.neon-button.secondary {
    border-color: #ff00ff;
    color: #ff00ff;
}

.neon-button.secondary:hover {
    background: rgba(255, 0, 255, 0.1);
    box-shadow: 
        0 0 20px #ff00ff,
        inset 0 0 20px rgba(255, 0, 255, 0.1);
}

/* Stats and Scores */
.final-score, .level-stats {
    margin: 30px 0;
}

.stat {
    margin: 15px 0;
}

.stat .label, .final-score .label {
    display: block;
    font-size: 14px;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff;
    margin-bottom: 5px;
    letter-spacing: 2px;
}

.stat .value, .final-score .value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 15px #ff00ff;
}

/* Controls Info */
.controls-info {
    margin-top: 40px;
    font-size: 0.9rem;
    color: #888;
    line-height: 1.6;
}

.controls-info p:first-child {
    color: #00ffff;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Animations */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes glow {
    0%, 100% { 
        box-shadow: 0 0 20px #00ffff;
    }
    50% { 
        box-shadow: 0 0 30px #00ffff, 0 0 40px #00ffff;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .screen-title {
        font-size: 2rem;
    }
    
    .top-bar {
        padding: 15px;
    }
    
    .top-bar .value {
        font-size: 20px;
    }
    
    .neon-button {
        font-size: 1rem;
        padding: 12px 25px;
    }
    
    .screen-content {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 2rem;
    }
    
    .top-bar {
        padding: 10px;
    }
    
    .top-bar .value {
        font-size: 18px;
    }
    
    .top-bar .label {
        font-size: 10px;
    }
}
