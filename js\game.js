// Main game engine for Neon Brick Breaker

class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = 800;
        this.height = 600;
        this.state = 'start'; // start, playing, paused, gameOver, victory
        
        // Game objects
        this.paddle = null;
        this.ball = null;
        this.bricks = [];
        this.powerUps = [];
        
        // Game state
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        this.ballLaunched = false;
        
        // Systems
        this.particleSystem = new ParticleSystem();
        this.trailSystem = new TrailSystem();
        
        // Timing
        this.lastTime = 0;
        this.deltaTime = 0;
        
        // Initialize
        this.setupCanvas();
        this.initializeGame();
    }
    
    setupCanvas() {
        this.canvas.width = this.width;
        this.canvas.height = this.height;
        
        // Make canvas responsive
        const resizeCanvas = () => {
            const container = this.canvas.parentElement;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;
            
            const scale = Math.min(
                containerWidth / this.width,
                containerHeight / this.height
            );
            
            this.canvas.style.width = (this.width * scale) + 'px';
            this.canvas.style.height = (this.height * scale) + 'px';
        };
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    }
    
    initializeGame() {
        this.createPaddle();
        this.createBall();
        this.createBricks();
    }
    
    createPaddle() {
        this.paddle = {
            x: this.width / 2 - 60,
            y: this.height - 40,
            width: 120,
            height: 15,
            speed: 400,
            color: Colors.neonBlue
        };
    }
    
    createBall() {
        this.ball = {
            x: this.width / 2,
            y: this.height - 60,
            radius: 8,
            vx: 0,
            vy: 0,
            speed: 300,
            color: Colors.neonPink,
            trail: []
        };
        this.ballLaunched = false;
    }
    
    createBricks() {
        this.bricks = [];
        const rows = 6;
        const cols = 10;
        const brickWidth = 70;
        const brickHeight = 25;
        const padding = 5;
        const offsetX = (this.width - (cols * (brickWidth + padding) - padding)) / 2;
        const offsetY = 80;
        
        const colors = [Colors.neonPink, Colors.neonBlue, Colors.neonGreen, 
                       Colors.neonPurple, Colors.neonYellow, Colors.neonOrange];
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                this.bricks.push({
                    x: offsetX + col * (brickWidth + padding),
                    y: offsetY + row * (brickHeight + padding),
                    width: brickWidth,
                    height: brickHeight,
                    color: colors[row % colors.length],
                    health: 1,
                    points: (rows - row) * 10,
                    destroyed: false
                });
            }
        }
    }
    
    update(currentTime) {
        this.deltaTime = Math.min((currentTime - this.lastTime) / 1000, 1/30);
        this.lastTime = currentTime;
        
        if (this.state !== 'playing') return;
        
        this.updatePaddle();
        this.updateBall();
        this.updateParticles();
        this.checkCollisions();
        this.checkGameState();
        
        Animation.screenShake.update(this.deltaTime);
    }
    
    updatePaddle() {
        const targetX = this.getInputX() - this.paddle.width / 2;
        
        // Smooth paddle movement
        const diff = targetX - this.paddle.x;
        this.paddle.x += diff * 0.15;
        
        // Keep paddle in bounds
        this.paddle.x = Utils.clamp(this.paddle.x, 0, this.width - this.paddle.width);
        
        // If ball not launched, keep it on paddle
        if (!this.ballLaunched) {
            this.ball.x = this.paddle.x + this.paddle.width / 2;
            this.ball.y = this.paddle.y - this.ball.radius - 5;
        }
    }
    
    updateBall() {
        if (!this.ballLaunched) return;
        
        // Update position
        this.ball.x += this.ball.vx * this.deltaTime;
        this.ball.y += this.ball.vy * this.deltaTime;
        
        // Add trail
        this.trailSystem.addTrailPoint(this.ball.x, this.ball.y, this.ball.color);
        
        // Wall collisions
        if (this.ball.x - this.ball.radius <= 0 || this.ball.x + this.ball.radius >= this.width) {
            this.ball.vx = -this.ball.vx;
            this.ball.x = Utils.clamp(this.ball.x, this.ball.radius, this.width - this.ball.radius);
            audioManager.play('wallHit');
            this.particleSystem.addSparkles(this.ball.x, this.ball.y, Colors.neonBlue, 5);
        }
        
        if (this.ball.y - this.ball.radius <= 0) {
            this.ball.vy = -this.ball.vy;
            this.ball.y = this.ball.radius;
            audioManager.play('wallHit');
            this.particleSystem.addSparkles(this.ball.x, this.ball.y, Colors.neonBlue, 5);
        }
        
        // Ball fell off screen
        if (this.ball.y > this.height + 50) {
            this.loseLife();
        }
    }
    
    updateParticles() {
        this.particleSystem.update(this.deltaTime);
        this.trailSystem.update(this.deltaTime);
    }
    
    checkCollisions() {
        // Paddle collision
        if (Utils.circleRectIntersect(
            this.ball.x, this.ball.y, this.ball.radius,
            this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height
        )) {
            // Calculate bounce angle based on hit position
            const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width;
            const bounceAngle = (hitPos - 0.5) * Math.PI * 0.6; // Max 54 degrees
            
            this.ball.vx = Math.sin(bounceAngle) * this.ball.speed;
            this.ball.vy = -Math.abs(Math.cos(bounceAngle)) * this.ball.speed;
            
            this.ball.y = this.paddle.y - this.ball.radius - 1;
            
            audioManager.play('paddleHit');
            this.particleSystem.addSparkles(this.ball.x, this.ball.y, this.paddle.color, 8);
        }
        
        // Brick collisions
        this.bricks.forEach(brick => {
            if (brick.destroyed) return;
            
            if (Utils.circleRectIntersect(
                this.ball.x, this.ball.y, this.ball.radius,
                brick.x, brick.y, brick.width, brick.height
            )) {
                // Determine collision side
                const side = Utils.getCollisionSide(
                    this.ball.x, this.ball.y, this.ball.radius,
                    brick.x, brick.y, brick.width, brick.height
                );
                
                // Bounce ball
                if (side === 'left' || side === 'right') {
                    this.ball.vx = -this.ball.vx;
                } else {
                    this.ball.vy = -this.ball.vy;
                }
                
                // Destroy brick
                this.destroyBrick(brick);
            }
        });
    }
    
    destroyBrick(brick) {
        brick.destroyed = true;
        this.score += brick.points;
        
        // Effects
        audioManager.play('brickHit');
        this.particleSystem.addExplosion(
            brick.x + brick.width / 2,
            brick.y + brick.height / 2,
            brick.color,
            12
        );
        Animation.screenShake.start(3, 0.1);
        
        // Update UI
        this.updateUI();
    }
    
    checkGameState() {
        // Check if all bricks destroyed
        const remainingBricks = this.bricks.filter(brick => !brick.destroyed);
        if (remainingBricks.length === 0) {
            this.levelComplete();
        }
    }
    
    levelComplete() {
        this.state = 'victory';
        audioManager.play('levelComplete');
        
        // Bonus points for remaining lives
        const bonus = this.lives * 100;
        this.score += bonus;
        
        this.showScreen('victoryScreen');
        this.updateUI();
    }
    
    loseLife() {
        this.lives--;
        this.trailSystem.clear();
        
        if (this.lives <= 0) {
            this.gameOver();
        } else {
            this.createBall();
            Animation.screenShake.start(5, 0.3);
        }
        
        this.updateUI();
    }
    
    gameOver() {
        this.state = 'gameOver';
        audioManager.play('gameOver');
        this.showScreen('gameOverScreen');
        
        // Update final score display
        document.getElementById('finalScore').textContent = this.score;
    }
    
    launchBall() {
        if (this.ballLaunched) return;
        
        this.ballLaunched = true;
        this.ball.vx = Utils.random(-100, 100);
        this.ball.vy = -this.ball.speed;
    }
    
    getInputX() {
        // Mouse input
        if (Input.mouse.x > 0) {
            return Input.mouse.x;
        }
        
        // Touch input
        const touch = Input.getPrimaryTouch();
        if (touch) {
            return touch.x;
        }
        
        // Keyboard input
        let targetX = this.paddle.x + this.paddle.width / 2;
        if (Input.isKeyPressed('ArrowLeft') || Input.isKeyPressed('KeyA')) {
            targetX -= this.paddle.speed * this.deltaTime;
        }
        if (Input.isKeyPressed('ArrowRight') || Input.isKeyPressed('KeyD')) {
            targetX += this.paddle.speed * this.deltaTime;
        }
        
        return targetX;
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('level').textContent = this.level;
    }
    
    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Show target screen
        document.getElementById(screenId).classList.add('active');
    }
    
    hideAllScreens() {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
    }
    
    startGame() {
        this.state = 'playing';
        this.hideAllScreens();
        this.updateUI();
    }
    
    pauseGame() {
        if (this.state === 'playing') {
            this.state = 'paused';
            this.showScreen('pauseScreen');
        }
    }
    
    resumeGame() {
        if (this.state === 'paused') {
            this.state = 'playing';
            this.hideAllScreens();
        }
    }
    
    resetGame() {
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        this.state = 'start';

        this.particleSystem.clear();
        this.trailSystem.clear();

        this.initializeGame();
        this.showScreen('startScreen');
        this.updateUI();
    }

    render() {
        // Clear canvas with dark background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.fillRect(0, 0, this.width, this.height);

        // Apply screen shake
        const shakeOffset = Animation.screenShake.getOffset();
        this.ctx.save();
        this.ctx.translate(shakeOffset.x, shakeOffset.y);

        // Render game objects
        this.renderBricks();
        this.renderPaddle();
        this.renderBall();

        // Render effects
        this.trailSystem.render(this.ctx);
        this.particleSystem.render(this.ctx);

        this.ctx.restore();

        // Add ambient floating particles occasionally
        if (Math.random() < 0.02) {
            this.particleSystem.addFloatingParticles(0, this.height, this.width, 0, 1);
        }
    }

    renderBricks() {
        this.bricks.forEach(brick => {
            if (brick.destroyed) return;

            this.ctx.save();

            // Glow effect
            this.ctx.shadowColor = brick.color;
            this.ctx.shadowBlur = 15;

            // Main brick
            this.ctx.fillStyle = brick.color;
            this.ctx.fillRect(brick.x, brick.y, brick.width, brick.height);

            // Inner glow
            const gradient = this.ctx.createLinearGradient(
                brick.x, brick.y, brick.x, brick.y + brick.height
            );
            gradient.addColorStop(0, brick.color + '80');
            gradient.addColorStop(0.5, brick.color + '40');
            gradient.addColorStop(1, brick.color + '80');

            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(brick.x + 2, brick.y + 2, brick.width - 4, brick.height - 4);

            // Border
            this.ctx.strokeStyle = brick.color;
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(brick.x, brick.y, brick.width, brick.height);

            this.ctx.restore();
        });
    }

    renderPaddle() {
        this.ctx.save();

        // Glow effect
        this.ctx.shadowColor = this.paddle.color;
        this.ctx.shadowBlur = 20;

        // Main paddle
        this.ctx.fillStyle = this.paddle.color;
        this.ctx.fillRect(this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height);

        // Gradient overlay
        const gradient = this.ctx.createLinearGradient(
            this.paddle.x, this.paddle.y, this.paddle.x, this.paddle.y + this.paddle.height
        );
        gradient.addColorStop(0, '#ffffff40');
        gradient.addColorStop(1, '#ffffff00');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height);

        this.ctx.restore();
    }

    renderBall() {
        this.ctx.save();

        // Outer glow
        const outerGlow = Colors.createGlowGradient(
            this.ctx, this.ball.x, this.ball.y, this.ball.radius * 3, this.ball.color
        );
        this.ctx.fillStyle = outerGlow;
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius * 3, 0, Math.PI * 2);
        this.ctx.fill();

        // Main ball
        this.ctx.fillStyle = this.ball.color;
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);
        this.ctx.fill();

        // Inner highlight
        const highlight = this.ctx.createRadialGradient(
            this.ball.x - this.ball.radius * 0.3, this.ball.y - this.ball.radius * 0.3, 0,
            this.ball.x, this.ball.y, this.ball.radius
        );
        highlight.addColorStop(0, '#ffffff80');
        highlight.addColorStop(1, '#ffffff00');

        this.ctx.fillStyle = highlight;
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.restore();
    }
}
