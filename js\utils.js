// Utility functions for the Neon Brick Breaker game

// Math utilities
const Utils = {
    // Clamp a value between min and max
    clamp: (value, min, max) => Math.max(min, Math.min(max, value)),
    
    // Linear interpolation
    lerp: (start, end, factor) => start + (end - start) * factor,
    
    // Distance between two points
    distance: (x1, y1, x2, y2) => Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2),
    
    // Random number between min and max
    random: (min, max) => Math.random() * (max - min) + min,
    
    // Random integer between min and max (inclusive)
    randomInt: (min, max) => Math.floor(Math.random() * (max - min + 1)) + min,
    
    // Normalize angle to 0-2π range
    normalizeAngle: (angle) => {
        while (angle < 0) angle += Math.PI * 2;
        while (angle >= Math.PI * 2) angle -= Math.PI * 2;
        return angle;
    },
    
    // Convert degrees to radians
    toRadians: (degrees) => degrees * Math.PI / 180,
    
    // Convert radians to degrees
    toDegrees: (radians) => radians * 180 / Math.PI,
    
    // Check if point is inside rectangle
    pointInRect: (px, py, rx, ry, rw, rh) => {
        return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
    },
    
    // Check if two rectangles intersect
    rectIntersect: (r1x, r1y, r1w, r1h, r2x, r2y, r2w, r2h) => {
        return !(r2x > r1x + r1w || 
                r2x + r2w < r1x || 
                r2y > r1y + r1h ||
                r2y + r2h < r1y);
    },
    
    // Check if circle intersects with rectangle
    circleRectIntersect: (cx, cy, radius, rx, ry, rw, rh) => {
        const closestX = Utils.clamp(cx, rx, rx + rw);
        const closestY = Utils.clamp(cy, ry, ry + rh);
        const distanceX = cx - closestX;
        const distanceY = cy - closestY;
        return (distanceX * distanceX + distanceY * distanceY) < (radius * radius);
    },
    
    // Get collision side for circle-rectangle collision
    getCollisionSide: (cx, cy, radius, rx, ry, rw, rh) => {
        const centerX = rx + rw / 2;
        const centerY = ry + rh / 2;
        const deltaX = cx - centerX;
        const deltaY = cy - centerY;
        
        // Calculate which side is closest
        const overlapX = (rw / 2 + radius) - Math.abs(deltaX);
        const overlapY = (rh / 2 + radius) - Math.abs(deltaY);
        
        if (overlapX < overlapY) {
            return deltaX > 0 ? 'right' : 'left';
        } else {
            return deltaY > 0 ? 'bottom' : 'top';
        }
    }
};

// Color utilities for neon effects
const Colors = {
    neonBlue: '#00ffff',
    neonPink: '#ff00ff',
    neonGreen: '#00ff00',
    neonPurple: '#8000ff',
    neonYellow: '#ffff00',
    neonOrange: '#ff8000',
    
    // Get a random neon color
    randomNeon: () => {
        const colors = [
            Colors.neonBlue,
            Colors.neonPink,
            Colors.neonGreen,
            Colors.neonPurple,
            Colors.neonYellow,
            Colors.neonOrange
        ];
        return colors[Utils.randomInt(0, colors.length - 1)];
    },
    
    // Create a glowing gradient
    createGlowGradient: (ctx, x, y, radius, color) => {
        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
        gradient.addColorStop(0, color);
        gradient.addColorStop(0.5, color + '80');
        gradient.addColorStop(1, color + '00');
        return gradient;
    },
    
    // Hex to RGB conversion
    hexToRgb: (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },
    
    // RGB to hex conversion
    rgbToHex: (r, g, b) => {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }
};

// Animation utilities
const Animation = {
    // Easing functions
    easeInOut: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    easeIn: (t) => t * t,
    easeOut: (t) => t * (2 - t),
    
    // Screen shake effect
    screenShake: {
        intensity: 0,
        duration: 0,
        timer: 0,
        
        start: (intensity, duration) => {
            Animation.screenShake.intensity = intensity;
            Animation.screenShake.duration = duration;
            Animation.screenShake.timer = duration;
        },
        
        update: (deltaTime) => {
            if (Animation.screenShake.timer > 0) {
                Animation.screenShake.timer -= deltaTime;
                if (Animation.screenShake.timer <= 0) {
                    Animation.screenShake.intensity = 0;
                }
            }
        },
        
        getOffset: () => {
            if (Animation.screenShake.intensity <= 0) return { x: 0, y: 0 };
            
            const progress = Animation.screenShake.timer / Animation.screenShake.duration;
            const currentIntensity = Animation.screenShake.intensity * progress;
            
            return {
                x: (Math.random() - 0.5) * currentIntensity,
                y: (Math.random() - 0.5) * currentIntensity
            };
        }
    }
};

// Input handling utilities
const Input = {
    mouse: { x: 0, y: 0, pressed: false },
    keys: {},
    touches: [],
    
    // Initialize input handlers
    init: (canvas) => {
        // Mouse events
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            Input.mouse.x = (e.clientX - rect.left) * (canvas.width / rect.width);
            Input.mouse.y = (e.clientY - rect.top) * (canvas.height / rect.height);
        });
        
        canvas.addEventListener('mousedown', () => Input.mouse.pressed = true);
        canvas.addEventListener('mouseup', () => Input.mouse.pressed = false);
        
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            Input.keys[e.code] = true;
            e.preventDefault();
        });
        
        document.addEventListener('keyup', (e) => {
            Input.keys[e.code] = false;
            e.preventDefault();
        });
        
        // Touch events
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            Input.touches = Array.from(e.touches).map(touch => {
                const rect = canvas.getBoundingClientRect();
                return {
                    x: (touch.clientX - rect.left) * (canvas.width / rect.width),
                    y: (touch.clientY - rect.top) * (canvas.height / rect.height)
                };
            });
        });
        
        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            Input.touches = Array.from(e.touches).map(touch => {
                const rect = canvas.getBoundingClientRect();
                return {
                    x: (touch.clientX - rect.left) * (canvas.width / rect.width),
                    y: (touch.clientY - rect.top) * (canvas.height / rect.height)
                };
            });
        });
        
        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            Input.touches = [];
        });
    },
    
    // Check if key is pressed
    isKeyPressed: (keyCode) => !!Input.keys[keyCode],
    
    // Get primary touch position
    getPrimaryTouch: () => Input.touches.length > 0 ? Input.touches[0] : null
};
